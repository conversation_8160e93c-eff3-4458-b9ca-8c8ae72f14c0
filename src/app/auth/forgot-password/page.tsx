'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { AuthLayout } from '@/shared/components/layout';
import { Button, Input } from '@/shared/components/ui';

export default function ForgotPasswordPage() {
  const router = useRouter();
  const [email, setEmail] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle send OTP logic here
    console.log('Send OTP to:', email);
    router.push('/auth/otp');
  };

  const handleBack = () => {
    router.push('/auth/login');
  };

  return (
    <AuthLayout
      title="Forgot Password"
      showBackButton={true}
      onBack={handleBack}
      contentType="simple"
    >
      <form onSubmit={handleSubmit} className="space-y-4 lg:space-y-6">
        <Input
          type="email"
          name="email"
          placeholder="Enter you Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          icon={
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          }
          label="Email"
        />

        <Button type="submit" className="w-full" size="lg">
          Send OTP
        </Button>
      </form>
    </AuthLayout>
  );
}
