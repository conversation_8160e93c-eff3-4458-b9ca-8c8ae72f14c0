'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { AuthLayout } from '@/shared/components/layout';
import { Button, OTPInput } from '@/shared/components/ui';

export default function OTPPage() {
  const router = useRouter();
  const [otp, setOtp] = useState('');

  const handleOTPComplete = (otpValue: string) => {
    setOtp(otpValue);
  };

  const handleVerify = () => {
    if (otp.length === 6) {
      // Handle OTP verification logic here
      console.log('Verify OTP:', otp);
      router.push('/auth/reset');
    }
  };

  const handleResend = () => {
    // Handle resend OTP logic here
    console.log('Resend OTP');
  };

  const handleBack = () => {
    router.push('/auth/forgot-password');
  };

  return (
    <AuthLayout
      title="Enter OTP"
      showBackButton={true}
      onBack={handleBack}
      contentType="simple"
    >
      <div className="space-y-8">
        <div className="text-center">
          <div className="flex items-center justify-center mb-6">
            <div className="flex items-center space-x-2">
              <span className="text-2xl font-bold text-gray-900">1</span>
              <div className="flex space-x-1">
                {[...Array(5)].map((_, index) => (
                  <div
                    key={index}
                    className="w-3 h-3 rounded-full bg-gray-300"
                  />
                ))}
              </div>
            </div>
          </div>
          
          <OTPInput
            length={6}
            onComplete={handleOTPComplete}
            onChange={setOtp}
          />
        </div>

        <Button 
          onClick={handleVerify} 
          className="w-full" 
          size="lg"
          disabled={otp.length !== 6}
        >
          Verify
        </Button>

        <div className="text-center">
          <span className="text-gray-600 text-sm">
            Haven't received OTP?{' '}
            <button
              type="button"
              onClick={handleResend}
              className="text-amber-600 hover:text-amber-700 font-medium"
            >
              Resend
            </button>
          </span>
        </div>
      </div>
    </AuthLayout>
  );
}
