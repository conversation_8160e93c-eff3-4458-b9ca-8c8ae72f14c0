// API related types

// Generic API response structure
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Pagination
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Error response
export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// Request/Response types for specific endpoints
export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role: 'client' | 'barber' | 'shop-owner';
  additionalData?: Record<string, any>;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface VerifyEmailRequest {
  email: string;
  verificationCode: string;
}

export interface ResetPasswordRequest {
  email: string;
  resetToken: string;
  newPassword: string;
}

export interface SendOTPRequest {
  email: string;
  type: 'password-reset' | 'email-verification';
}

export interface VerifyOTPRequest {
  email: string;
  otp: string;
  type: 'password-reset' | 'email-verification';
}

// File upload
export interface FileUploadResponse {
  url: string;
  filename: string;
  size: number;
  mimeType: string;
}
