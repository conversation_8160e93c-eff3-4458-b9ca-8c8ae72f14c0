import { ClientPreferences } from "./onboarding";

// Authentication related types
export type UserRole = 'client' | 'barber' | 'shop-owner';
export type PlanType = 'annual' | 'monthly';

// Base user interface
export interface BaseUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  createdAt: Date;
  updatedAt: Date;
}

// Client specific data
export interface ClientFormData {
  firstName: string;
  lastName: string;
  email: string;
  location: string;
  password: string;
}

export interface Client extends BaseUser {
  role: 'client';
  location: string;
  preferences?: ClientPreferences;
}

// Barber specific data
export interface Address {
  street: string;
  zipcode: string;
  city: string;
  state: string;
}

export interface BarberFormData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  experience: string;
  language: string;
  address: Address;
  vatNumber: string;
  barberCertificate: File | null;
  password: string;
}

export interface Barber extends BaseUser {
  role: 'barber';
  phoneNumber: string;
  experience: string;
  language: string;
  address: Address;
  vatNumber: string;
  barberCertificate?: string; // URL to uploaded certificate
  isVerified: boolean;
}

// Shop Owner specific data
export interface ShopOwnerFormData {
  shopName: string;
  shopOwnerName: string;
  shopEmail: string;
  phoneNumber: string;
  experience: string;
  language: string;
  address: Address;
  vatNumber: string;
  barberCertificate: File | null;
  password: string;
}

export interface ShopOwner extends BaseUser {
  role: 'shop-owner';
  shopName: string;
  shopOwnerName: string;
  shopEmail: string;
  phoneNumber: string;
  experience: string;
  language: string;
  address: Address;
  vatNumber: string;
  barberCertificate?: string; // URL to uploaded certificate
  isVerified: boolean;
}

// Union type for all users
export type User = Client | Barber | ShopOwner;

// Authentication state
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Login/Register responses
export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

// Login form data
export interface LoginFormData {
  email: string;
  password: string;
}

// Password reset data
export interface ResetPasswordFormData {
  password: string;
  confirmPassword: string;
}

// OTP verification
export interface OTPVerificationData {
  email: string;
  otp: string;
}

// Email verification
export interface EmailVerificationData {
  email: string;
  verificationCode: string;
}
