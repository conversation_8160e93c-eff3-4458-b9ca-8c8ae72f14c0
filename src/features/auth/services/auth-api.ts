// Authentication API service
import { 
  LoginFormData, 
  AuthResponse, 
  User, 
  UserRole,
  ClientFormData,
  BarberFormData,
  ShopOwnerFormData,
  OTPVerificationData,
  EmailVerificationData,
  ResetPasswordFormData
} from '@/types/auth';
import { ApiResponse } from '@/types/api';
import { API_CONFIG, STORAGE_KEYS } from '@/shared/constants';

class AuthApiService {
  private baseUrl = API_CONFIG.BASE_URL;

  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const token = this.getStoredToken();

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Request failed');
      }

      return data;
    } catch (error) {
      console.error('API Request failed:', error);
      throw error;
    }
  }

  private getStoredToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  }

  private storeTokens(authResponse: AuthResponse): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, authResponse.token);
    localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, authResponse.refreshToken);
    localStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(authResponse.user));
  }

  private clearTokens(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.USER_DATA);
  }

  async login(credentials: LoginFormData): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    if (response.success && response.data) {
      this.storeTokens(response.data);
      return response.data;
    }

    throw new Error(response.error || 'Login failed');
  }

  async register(
    userData: ClientFormData | BarberFormData | ShopOwnerFormData, 
    role: UserRole
  ): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/auth/register', {
      method: 'POST',
      body: JSON.stringify({ ...userData, role }),
    });

    if (response.success && response.data) {
      this.storeTokens(response.data);
      return response.data;
    }

    throw new Error(response.error || 'Registration failed');
  }

  async verifyEmail(data: EmailVerificationData): Promise<void> {
    const response = await this.request<void>('/auth/verify-email', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!response.success) {
      throw new Error(response.error || 'Email verification failed');
    }
  }

  async sendOTP(email: string, type: 'password-reset' | 'email-verification'): Promise<void> {
    const response = await this.request<void>('/auth/send-otp', {
      method: 'POST',
      body: JSON.stringify({ email, type }),
    });

    if (!response.success) {
      throw new Error(response.error || 'Failed to send OTP');
    }
  }

  async verifyOTP(data: OTPVerificationData): Promise<void> {
    const response = await this.request<void>('/auth/verify-otp', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!response.success) {
      throw new Error(response.error || 'OTP verification failed');
    }
  }

  async resetPassword(data: ResetPasswordFormData & { resetToken: string }): Promise<void> {
    const response = await this.request<void>('/auth/reset-password', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!response.success) {
      throw new Error(response.error || 'Password reset failed');
    }
  }

  async refreshToken(): Promise<AuthResponse> {
    const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await this.request<AuthResponse>('/auth/refresh', {
      method: 'POST',
      body: JSON.stringify({ refreshToken }),
    });

    if (response.success && response.data) {
      this.storeTokens(response.data);
      return response.data;
    }

    throw new Error(response.error || 'Token refresh failed');
  }

  async logout(): Promise<void> {
    try {
      await this.request<void>('/auth/logout', {
        method: 'POST',
      });
    } catch (error) {
      console.error('Logout request failed:', error);
    } finally {
      this.clearTokens();
    }
  }

  getCurrentUser(): User | null {
    if (typeof window === 'undefined') return null;
    const userData = localStorage.getItem(STORAGE_KEYS.USER_DATA);
    return userData ? JSON.parse(userData) : null;
  }

  isAuthenticated(): boolean {
    return !!this.getStoredToken();
  }
}

export const authApiService = new AuthApiService();
