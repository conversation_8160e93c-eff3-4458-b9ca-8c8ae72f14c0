'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button, Input, Select, FileUpload } from '@/shared/components/ui';
import Image from 'next/image';

interface BarberFormData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  experience: string;
  language: string;
  address: {
    street: string;
    zipcode: string;
    city: string;
    state: string;
  };
  vatNumber: string;
  barberCertificate: File | null;
  password: string;
}

const experienceOptions = [
  { value: '0-1', label: '0-1 years' },
  { value: '1-3', label: '1-3 years' },
  { value: '3-5', label: '3-5 years' },
  { value: '5-10', label: '5-10 years' },
  { value: '10+', label: '10+ years' },
];

const languageOptions = [
  { value: 'en', label: 'English' },
  { value: 'es', label: 'Spanish' },
  { value: 'fr', label: 'French' },
  { value: 'de', label: 'German' },
  { value: 'it', label: 'Italian' },
  { value: 'pt', label: 'Portuguese' },
  { value: 'nl', label: 'Dutch' },
  { value: 'ar', label: 'Arabic' },
];

export function SignupBarberForm() {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState<BarberFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    experience: '',
    language: '',
    address: {
      street: '',
      zipcode: '',
      city: '',
      state: '',
    },
    vatNumber: '',
    barberCertificate: null,
    password: '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    
    if (name.startsWith('address.')) {
      const addressField = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        address: {
          ...prev.address,
          [addressField]: value,
        },
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleSelectChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleFileChange = (file: File | null) => {
    setFormData(prev => ({
      ...prev,
      barberCertificate: file,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Barber registration:', formData);
    // Navigate to email verification
    router.push('/auth/verify-email?type=barber');
  };

  const handleGoogleSignUp = () => {
    console.log('Google sign-up clicked');
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 lg:space-y-5">
      {/* Name Fields */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 lg:gap-4">
        <Input
          type="text"
          name="firstName"
          placeholder="Enter your First Name"
          value={formData.firstName}
          onChange={handleInputChange}
          required
          icon={
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          }
          label="First Name"
        />
        
        <Input
          type="text"
          name="lastName"
          placeholder="Enter your Last Name"
          value={formData.lastName}
          onChange={handleInputChange}
          required
          icon={
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          }
          label="Last Name"
        />
      </div>

      {/* Email */}
      <Input
        type="email"
        name="email"
        placeholder="Enter your Email"
        value={formData.email}
        onChange={handleInputChange}
        required
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
          </svg>
        }
        label="Email"
      />

      {/* Phone Number */}
      <Input
        type="tel"
        name="phoneNumber"
        placeholder="Enter your Phone Number"
        value={formData.phoneNumber}
        onChange={handleInputChange}
        required
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
          </svg>
        }
        label="Phone Number"
      />

      {/* Experience */}
      <Select
        options={experienceOptions}
        value={formData.experience}
        onChange={(value) => handleSelectChange('experience', value)}
        placeholder="Select"
        label="Experience"
        required
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0H8" />
          </svg>
        }
      />

      {/* Language */}
      <Select
        options={languageOptions}
        value={formData.language}
        onChange={(value) => handleSelectChange('language', value)}
        placeholder="Select the language you are fluent in"
        label="Language"
        required
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
          </svg>
        }
      />

      {/* Address */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          Address <span className="text-red-500">*</span>
        </label>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 lg:gap-4">
          <Input
            type="text"
            name="address.street"
            placeholder="Street, Building"
            value={formData.address.street}
            onChange={handleInputChange}
            required
            icon={
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            }
          />
          
          <Input
            type="text"
            name="address.zipcode"
            placeholder="Zip Code"
            value={formData.address.zipcode}
            onChange={handleInputChange}
            required
          />
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 lg:gap-4">
          <Input
            type="text"
            name="address.city"
            placeholder="City"
            value={formData.address.city}
            onChange={handleInputChange}
            required
          />
          
          <Input
            type="text"
            name="address.state"
            placeholder="State/Province"
            value={formData.address.state}
            onChange={handleInputChange}
            required
          />
        </div>
      </div>

      {/* VAT Number */}
      <Input
        type="text"
        name="vatNumber"
        placeholder="Enter your VAT Number"
        value={formData.vatNumber}
        onChange={handleInputChange}
        required
        icon={
          <span className="text-gray-400 font-medium">#</span>
        }
        label="VAT Number"
      />

      {/* Barber Certificate */}
      <FileUpload
        label="Barbers Certificate"
        placeholder="Upload your Barbers Certificate"
        accept=".pdf,.jpg,.jpeg,.png"
        onChange={handleFileChange}
        required
      />

      {/* Password */}
      <Input
        type={showPassword ? 'text' : 'password'}
        name="password"
        placeholder="• • • • • • • •"
        value={formData.password}
        onChange={handleInputChange}
        required
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
        }
        rightIcon={
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="hover:text-gray-600"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {showPassword ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              )}
            </svg>
          </button>
        }
        label="Password"
      />

      {/* Submit Button */}
      <Button type="submit" className="w-full" size="lg">
        Register
      </Button>

      {/* Divider */}
      <div className="text-center">
        <span className="text-gray-500 text-sm">or Continue with</span>
      </div>

      {/* Google Sign Up */}
      <Button
        type="button"
        variant="outline"
        onClick={handleGoogleSignUp}
        className="w-full flex items-center justify-center gap-3"
        size="lg"
      >
        <Image
          src="/assets/images/google-logo.svg"
          alt="Google"
          width={20}
          height={20}
        />
        Sign up with Google
      </Button>

      {/* Login Link */}
      <div className="text-center">
        <span className="text-gray-600 text-sm">
          Already have an account?{' '}
          <button
            type="button"
            onClick={() => router.push('/auth/login')}
            className="text-amber-600 hover:text-amber-700 font-medium"
          >
            Login
          </button>
        </span>
      </div>
    </form>
  );
}
